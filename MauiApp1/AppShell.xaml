<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="MauiApp1.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:MauiApp1"
    xmlns:views="clr-namespace:MauiApp1.Views"
    Title="POS System">

    <TabBar BackgroundColor="White"
            BarTextColor="{AppThemeBinding Light={StaticResource Gray600}, Dark={StaticResource Gray400}}"
            SelectedTabColor="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource PrimaryLight}}"
            UnselectedTabColor="{AppThemeBinding Light={StaticResource Gray500}, Dark={StaticResource Gray500}}">

        <ShellContent
            Title="Products"
            Icon="🛍️"
            ContentTemplate="{DataTemplate local:MainPage}"
            Route="main" />

        <ShellContent
            Title="Cart"
            Icon="🛒"
            ContentTemplate="{DataTemplate views:CartPage}"
            Route="cart" />
    </TabBar>

    <!-- Modal pages -->
    <ShellContent
        ContentTemplate="{DataTemplate views:PaymentPage}"
        Route="payment" />

</Shell>
